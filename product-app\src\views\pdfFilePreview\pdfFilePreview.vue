<template>
  <div class="pdfFilePreview">
    <van-sticky>
      <van-nav-bar v-if="hasApi" :title="title" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <template v-if="pdfUrl">
      <!-- PDF 容器，支持手势缩放 -->
      <div ref="pdfContainer" class="pdf-container" @touchstart="handleTouchStart" @touchmove="handleTouchMove"
        @touchend="handleTouchEnd" @wheel="handleWheel">
        <canvas ref="pdfCanvas" class="pdf-canvas" :style="canvasStyle"></canvas>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-overlay">
          <van-loading color="#1989fa" />
          <div>PDF加载中...</div>
        </div>

        <!-- 控制按钮 -->
        <div class="pdf-controls">
          <van-button size="small" @click="zoomOut">-</van-button>
          <span class="zoom-text">{{ Math.round(scale * 100) }}%</span>
          <van-button size="small" @click="zoomIn">+</van-button>
          <van-button size="small" @click="resetZoom">重置</van-button>
        </div>
      </div>
    </template>
    <template v-else>
      <div style="color:#fff;text-align:center;padding-top:40vh;">未获取到PDF地址</div>
    </template>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { computed, ref, onMounted, nextTick } from 'vue'
import { NavBar, Sticky, Button, Loading } from 'vant'

export default {
  name: 'pdfFilePreview',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Button.name]: Button,
    [Loading.name]: Loading
  },
  setup () {
    const route = useRoute()
    const pdfUrl = computed(() => route.query.url)
    const hasApi = ref(true)
    const title = ref(route.query.title)
    const loading = ref(false)

    // PDF 相关状态
    const pdfContainer = ref(null)
    const pdfCanvas = ref(null)
    const scale = ref(1)
    const translateX = ref(0)
    const translateY = ref(0)
    const pdfDoc = ref(null)
    const currentPage = ref(1)

    // 触摸相关状态
    const lastTouchDistance = ref(0)
    const lastTouchCenter = ref({ x: 0, y: 0 })
    const isDragging = ref(false)
    const lastTouchPos = ref({ x: 0, y: 0 })

    // 计算样式
    const canvasStyle = computed(() => ({
      transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value})`,
      transformOrigin: 'center center',
      transition: isDragging.value ? 'none' : 'transform 0.3s ease'
    }))

    if (title.value) {
      document.title = title.value
    }

    // 加载 PDF.js
    const loadPDFJS = async () => {
      if (window.pdfjsLib) return

      // 动态加载 PDF.js
      const script = document.createElement('script')
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
      document.head.appendChild(script)

      return new Promise((resolve) => {
        script.onload = () => {
          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
          resolve()
        }
      })
    }

    // 渲染 PDF
    const renderPDF = async () => {
      if (!pdfUrl.value || !pdfCanvas.value) return

      loading.value = true

      try {
        await loadPDFJS()

        const loadingTask = window.pdfjsLib.getDocument(pdfUrl.value)
        pdfDoc.value = await loadingTask.promise

        await renderPage(currentPage.value)
      } catch (error) {
        console.error('PDF 加载失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 渲染页面
    const renderPage = async (pageNum) => {
      if (!pdfDoc.value || !pdfCanvas.value) return

      const page = await pdfDoc.value.getPage(pageNum)
      const canvas = pdfCanvas.value
      const context = canvas.getContext('2d')

      const viewport = page.getViewport({ scale: 1.5 })
      canvas.height = viewport.height
      canvas.width = viewport.width

      const renderContext = {
        canvasContext: context,
        viewport: viewport
      }

      await page.render(renderContext).promise
    }

    onMounted(async () => {
      if (typeof (api) === 'undefined') {
        hasApi.value = false
      } else {
        hasApi.value = true
      }

      await nextTick()
      if (pdfUrl.value) {
        await renderPDF()
      }
    })

    const onClickLeft = () => {
      if (typeof (api) === 'undefined') return history.back()
      // eslint-disable-next-line no-undef
      api.closeWin()
    }

    // 缩放控制
    const zoomIn = () => {
      scale.value = Math.min(scale.value * 1.2, 3)
    }

    const zoomOut = () => {
      scale.value = Math.max(scale.value / 1.2, 0.5)
    }

    const resetZoom = () => {
      scale.value = 1
      translateX.value = 0
      translateY.value = 0
    }

    // 获取两点间距离
    const getDistance = (touch1, touch2) => {
      const dx = touch1.clientX - touch2.clientX
      const dy = touch1.clientY - touch2.clientY
      return Math.sqrt(dx * dx + dy * dy)
    }

    // 获取两点中心
    const getCenter = (touch1, touch2) => {
      return {
        x: (touch1.clientX + touch2.clientX) / 2,
        y: (touch1.clientY + touch2.clientY) / 2
      }
    }

    // 触摸开始
    const handleTouchStart = (e) => {
      e.preventDefault()

      if (e.touches.length === 1) {
        // 单指拖拽
        isDragging.value = true
        lastTouchPos.value = {
          x: e.touches[0].clientX,
          y: e.touches[0].clientY
        }
      } else if (e.touches.length === 2) {
        // 双指缩放
        isDragging.value = false
        lastTouchDistance.value = getDistance(e.touches[0], e.touches[1])
        lastTouchCenter.value = getCenter(e.touches[0], e.touches[1])
      }
    }

    // 触摸移动
    const handleTouchMove = (e) => {
      e.preventDefault()

      if (e.touches.length === 1 && isDragging.value) {
        // 单指拖拽
        const deltaX = e.touches[0].clientX - lastTouchPos.value.x
        const deltaY = e.touches[0].clientY - lastTouchPos.value.y

        translateX.value += deltaX
        translateY.value += deltaY

        lastTouchPos.value = {
          x: e.touches[0].clientX,
          y: e.touches[0].clientY
        }
      } else if (e.touches.length === 2) {
        // 双指缩放
        const distance = getDistance(e.touches[0], e.touches[1])
        const center = getCenter(e.touches[0], e.touches[1])

        if (lastTouchDistance.value > 0) {
          const scaleChange = distance / lastTouchDistance.value
          const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))

          // 以触摸中心为缩放中心
          const rect = pdfContainer.value.getBoundingClientRect()
          const centerX = center.x - rect.left
          const centerY = center.y - rect.top

          const scaleDiff = newScale - scale.value
          translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value
          translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value

          scale.value = newScale
        }

        lastTouchDistance.value = distance
        lastTouchCenter.value = center
      }
    }

    // 触摸结束
    const handleTouchEnd = (e) => {
      e.preventDefault()
      isDragging.value = false
      lastTouchDistance.value = 0
    }

    // 鼠标滚轮缩放
    const handleWheel = (e) => {
      e.preventDefault()

      const rect = pdfContainer.value.getBoundingClientRect()
      const centerX = e.clientX - rect.left
      const centerY = e.clientY - rect.top

      const scaleChange = e.deltaY > 0 ? 0.9 : 1.1
      const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))

      const scaleDiff = newScale - scale.value
      translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value
      translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value

      scale.value = newScale
    }

    return {
      pdfUrl,
      onClickLeft,
      title,
      hasApi,
      loading,
      pdfContainer,
      pdfCanvas,
      scale,
      canvasStyle,
      zoomIn,
      zoomOut,
      resetZoom,
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      handleWheel
    }
  }
}
</script>
<style lang="less">
.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}

.pdfFilePreview {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

.pdf-container {
  position: relative;
  width: 100%;
  height: calc(100% - 35px);
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: none;
  /* 禁用默认触摸行为 */
}

.pdf-canvas {
  max-width: 100%;
  max-height: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #666;
  z-index: 10;
}

.pdf-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 20px;
  z-index: 20;

  .van-button {
    min-width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .zoom-text {
    color: white;
    font-size: 14px;
    min-width: 50px;
    text-align: center;
  }
}
</style>
