{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue", "mtime": 1756714397355}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyB1c2VSb3V0ZSB9IGZyb20gJ3Z1ZS1yb3V0ZXInDQppbXBvcnQgeyBjb21wdXRlZCwgcmVmLCBvbk1vdW50ZWQsIG5leHRUaWNrIH0gZnJvbSAndnVlJw0KaW1wb3J0IHsgTmF2QmFyLCBTdGlja3ksIEJ1dHRvbiwgTG9hZGluZyB9IGZyb20gJ3ZhbnQnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ3BkZkZpbGVQcmV2aWV3JywNCiAgY29tcG9uZW50czogew0KICAgIFtOYXZCYXIubmFtZV06IE5hdkJhciwNCiAgICBbU3RpY2t5Lm5hbWVdOiBTdGlja3ksDQogICAgW0J1dHRvbi5uYW1lXTogQnV0dG9uLA0KICAgIFtMb2FkaW5nLm5hbWVdOiBMb2FkaW5nDQogIH0sDQogIHNldHVwICgpIHsNCiAgICBjb25zdCByb3V0ZSA9IHVzZVJvdXRlKCkNCiAgICBjb25zdCBwZGZVcmwgPSBjb21wdXRlZCgoKSA9PiByb3V0ZS5xdWVyeS51cmwpDQogICAgY29uc3QgaGFzQXBpID0gcmVmKHRydWUpDQogICAgY29uc3QgdGl0bGUgPSByZWYocm91dGUucXVlcnkudGl0bGUpDQogICAgY29uc3QgbG9hZGluZyA9IHJlZihmYWxzZSkNCg0KICAgIC8vIFBERiDnm7jlhbPnirbmgIENCiAgICBjb25zdCBwZGZDb250YWluZXIgPSByZWYobnVsbCkNCiAgICBjb25zdCBwZGZDYW52YXMgPSByZWYobnVsbCkNCiAgICBjb25zdCBzY2FsZSA9IHJlZigxKQ0KICAgIGNvbnN0IHRyYW5zbGF0ZVggPSByZWYoMCkNCiAgICBjb25zdCB0cmFuc2xhdGVZID0gcmVmKDApDQogICAgY29uc3QgcGRmRG9jID0gcmVmKG51bGwpDQogICAgY29uc3QgY3VycmVudFBhZ2UgPSByZWYoMSkNCg0KICAgIC8vIOinpuaRuOebuOWFs+eKtuaAgQ0KICAgIGNvbnN0IGxhc3RUb3VjaERpc3RhbmNlID0gcmVmKDApDQogICAgY29uc3QgbGFzdFRvdWNoQ2VudGVyID0gcmVmKHsgeDogMCwgeTogMCB9KQ0KICAgIGNvbnN0IGlzRHJhZ2dpbmcgPSByZWYoZmFsc2UpDQogICAgY29uc3QgbGFzdFRvdWNoUG9zID0gcmVmKHsgeDogMCwgeTogMCB9KQ0KDQogICAgLy8g6K6h566X5qC35byPDQogICAgY29uc3QgY2FudmFzU3R5bGUgPSBjb21wdXRlZCgoKSA9PiAoew0KICAgICAgdHJhbnNmb3JtOiBgdHJhbnNsYXRlKCR7dHJhbnNsYXRlWC52YWx1ZX1weCwgJHt0cmFuc2xhdGVZLnZhbHVlfXB4KSBzY2FsZSgke3NjYWxlLnZhbHVlfSlgLA0KICAgICAgdHJhbnNmb3JtT3JpZ2luOiAnY2VudGVyIGNlbnRlcicsDQogICAgICB0cmFuc2l0aW9uOiBpc0RyYWdnaW5nLnZhbHVlID8gJ25vbmUnIDogJ3RyYW5zZm9ybSAwLjNzIGVhc2UnDQogICAgfSkpDQoNCiAgICBpZiAodGl0bGUudmFsdWUpIHsNCiAgICAgIGRvY3VtZW50LnRpdGxlID0gdGl0bGUudmFsdWUNCiAgICB9DQoNCiAgICAvLyDliqDovb0gUERGLmpzDQogICAgY29uc3QgbG9hZFBERkpTID0gYXN5bmMgKCkgPT4gew0KICAgICAgaWYgKHdpbmRvdy5wZGZqc0xpYikgcmV0dXJuDQoNCiAgICAgIC8vIOWKqOaAgeWKoOi9vSBQREYuanMNCiAgICAgIGNvbnN0IHNjcmlwdCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NjcmlwdCcpDQogICAgICBzY3JpcHQuc3JjID0gJ2h0dHBzOi8vY2RuanMuY2xvdWRmbGFyZS5jb20vYWpheC9saWJzL3BkZi5qcy8zLjExLjE3NC9wZGYubWluLmpzJw0KICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChzY3JpcHQpDQoNCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gew0KICAgICAgICBzY3JpcHQub25sb2FkID0gKCkgPT4gew0KICAgICAgICAgIHdpbmRvdy5wZGZqc0xpYi5HbG9iYWxXb3JrZXJPcHRpb25zLndvcmtlclNyYyA9ICdodHRwczovL2NkbmpzLmNsb3VkZmxhcmUuY29tL2FqYXgvbGlicy9wZGYuanMvMy4xMS4xNzQvcGRmLndvcmtlci5taW4uanMnDQogICAgICAgICAgcmVzb2x2ZSgpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfQ0KDQogICAgLy8g5riy5p+TIFBERg0KICAgIGNvbnN0IHJlbmRlclBERiA9IGFzeW5jICgpID0+IHsNCiAgICAgIGlmICghcGRmVXJsLnZhbHVlIHx8ICFwZGZDYW52YXMudmFsdWUpIHJldHVybg0KDQogICAgICBsb2FkaW5nLnZhbHVlID0gdHJ1ZQ0KDQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCBsb2FkUERGSlMoKQ0KDQogICAgICAgIGNvbnN0IGxvYWRpbmdUYXNrID0gd2luZG93LnBkZmpzTGliLmdldERvY3VtZW50KHBkZlVybC52YWx1ZSkNCiAgICAgICAgcGRmRG9jLnZhbHVlID0gYXdhaXQgbG9hZGluZ1Rhc2sucHJvbWlzZQ0KDQogICAgICAgIGF3YWl0IHJlbmRlclBhZ2UoY3VycmVudFBhZ2UudmFsdWUpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdQREYg5Yqg6L295aSx6LSlOicsIGVycm9yKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgbG9hZGluZy52YWx1ZSA9IGZhbHNlDQogICAgICB9DQogICAgfQ0KDQogICAgLy8g5riy5p+T6aG16Z2iDQogICAgY29uc3QgcmVuZGVyUGFnZSA9IGFzeW5jIChwYWdlTnVtKSA9PiB7DQogICAgICBpZiAoIXBkZkRvYy52YWx1ZSB8fCAhcGRmQ2FudmFzLnZhbHVlKSByZXR1cm4NCg0KICAgICAgY29uc3QgcGFnZSA9IGF3YWl0IHBkZkRvYy52YWx1ZS5nZXRQYWdlKHBhZ2VOdW0pDQogICAgICBjb25zdCBjYW52YXMgPSBwZGZDYW52YXMudmFsdWUNCiAgICAgIGNvbnN0IGNvbnRleHQgPSBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKQ0KDQogICAgICBjb25zdCB2aWV3cG9ydCA9IHBhZ2UuZ2V0Vmlld3BvcnQoeyBzY2FsZTogMS41IH0pDQogICAgICBjYW52YXMuaGVpZ2h0ID0gdmlld3BvcnQuaGVpZ2h0DQogICAgICBjYW52YXMud2lkdGggPSB2aWV3cG9ydC53aWR0aA0KDQogICAgICBjb25zdCByZW5kZXJDb250ZXh0ID0gew0KICAgICAgICBjYW52YXNDb250ZXh0OiBjb250ZXh0LA0KICAgICAgICB2aWV3cG9ydDogdmlld3BvcnQNCiAgICAgIH0NCg0KICAgICAgYXdhaXQgcGFnZS5yZW5kZXIocmVuZGVyQ29udGV4dCkucHJvbWlzZQ0KICAgIH0NCg0KICAgIG9uTW91bnRlZChhc3luYyAoKSA9PiB7DQogICAgICBpZiAodHlwZW9mIChhcGkpID09PSAndW5kZWZpbmVkJykgew0KICAgICAgICBoYXNBcGkudmFsdWUgPSBmYWxzZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaGFzQXBpLnZhbHVlID0gdHJ1ZQ0KICAgICAgfQ0KDQogICAgICBhd2FpdCBuZXh0VGljaygpDQogICAgICBpZiAocGRmVXJsLnZhbHVlKSB7DQogICAgICAgIGF3YWl0IHJlbmRlclBERigpDQogICAgICB9DQogICAgfSkNCg0KICAgIGNvbnN0IG9uQ2xpY2tMZWZ0ID0gKCkgPT4gew0KICAgICAgaWYgKHR5cGVvZiAoYXBpKSA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBoaXN0b3J5LmJhY2soKQ0KICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVuZGVmDQogICAgICBhcGkuY2xvc2VXaW4oKQ0KICAgIH0NCg0KICAgIC8vIOe8qeaUvuaOp+WItg0KICAgIGNvbnN0IHpvb21JbiA9ICgpID0+IHsNCiAgICAgIHNjYWxlLnZhbHVlID0gTWF0aC5taW4oc2NhbGUudmFsdWUgKiAxLjIsIDMpDQogICAgfQ0KDQogICAgY29uc3Qgem9vbU91dCA9ICgpID0+IHsNCiAgICAgIHNjYWxlLnZhbHVlID0gTWF0aC5tYXgoc2NhbGUudmFsdWUgLyAxLjIsIDAuNSkNCiAgICB9DQoNCiAgICBjb25zdCByZXNldFpvb20gPSAoKSA9PiB7DQogICAgICBzY2FsZS52YWx1ZSA9IDENCiAgICAgIHRyYW5zbGF0ZVgudmFsdWUgPSAwDQogICAgICB0cmFuc2xhdGVZLnZhbHVlID0gMA0KICAgIH0NCg0KICAgIC8vIOiOt+WPluS4pOeCuemXtOi3neemuw0KICAgIGNvbnN0IGdldERpc3RhbmNlID0gKHRvdWNoMSwgdG91Y2gyKSA9PiB7DQogICAgICBjb25zdCBkeCA9IHRvdWNoMS5jbGllbnRYIC0gdG91Y2gyLmNsaWVudFgNCiAgICAgIGNvbnN0IGR5ID0gdG91Y2gxLmNsaWVudFkgLSB0b3VjaDIuY2xpZW50WQ0KICAgICAgcmV0dXJuIE1hdGguc3FydChkeCAqIGR4ICsgZHkgKiBkeSkNCiAgICB9DQoNCiAgICAvLyDojrflj5bkuKTngrnkuK3lv4MNCiAgICBjb25zdCBnZXRDZW50ZXIgPSAodG91Y2gxLCB0b3VjaDIpID0+IHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIHg6ICh0b3VjaDEuY2xpZW50WCArIHRvdWNoMi5jbGllbnRYKSAvIDIsDQogICAgICAgIHk6ICh0b3VjaDEuY2xpZW50WSArIHRvdWNoMi5jbGllbnRZKSAvIDINCiAgICAgIH0NCiAgICB9DQoNCiAgICAvLyDop6bmkbjlvIDlp4sNCiAgICBjb25zdCBoYW5kbGVUb3VjaFN0YXJ0ID0gKGUpID0+IHsNCiAgICAgIGUucHJldmVudERlZmF1bHQoKQ0KDQogICAgICBpZiAoZS50b3VjaGVzLmxlbmd0aCA9PT0gMSkgew0KICAgICAgICAvLyDljZXmjIfmi5bmi70NCiAgICAgICAgaXNEcmFnZ2luZy52YWx1ZSA9IHRydWUNCiAgICAgICAgbGFzdFRvdWNoUG9zLnZhbHVlID0gew0KICAgICAgICAgIHg6IGUudG91Y2hlc1swXS5jbGllbnRYLA0KICAgICAgICAgIHk6IGUudG91Y2hlc1swXS5jbGllbnRZDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAoZS50b3VjaGVzLmxlbmd0aCA9PT0gMikgew0KICAgICAgICAvLyDlj4zmjIfnvKnmlL4NCiAgICAgICAgaXNEcmFnZ2luZy52YWx1ZSA9IGZhbHNlDQogICAgICAgIGxhc3RUb3VjaERpc3RhbmNlLnZhbHVlID0gZ2V0RGlzdGFuY2UoZS50b3VjaGVzWzBdLCBlLnRvdWNoZXNbMV0pDQogICAgICAgIGxhc3RUb3VjaENlbnRlci52YWx1ZSA9IGdldENlbnRlcihlLnRvdWNoZXNbMF0sIGUudG91Y2hlc1sxXSkNCiAgICAgIH0NCiAgICB9DQoNCiAgICAvLyDop6bmkbjnp7vliqgNCiAgICBjb25zdCBoYW5kbGVUb3VjaE1vdmUgPSAoZSkgPT4gew0KICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpDQoNCiAgICAgIGlmIChlLnRvdWNoZXMubGVuZ3RoID09PSAxICYmIGlzRHJhZ2dpbmcudmFsdWUpIHsNCiAgICAgICAgLy8g5Y2V5oyH5ouW5ou9DQogICAgICAgIGNvbnN0IGRlbHRhWCA9IGUudG91Y2hlc1swXS5jbGllbnRYIC0gbGFzdFRvdWNoUG9zLnZhbHVlLngNCiAgICAgICAgY29uc3QgZGVsdGFZID0gZS50b3VjaGVzWzBdLmNsaWVudFkgLSBsYXN0VG91Y2hQb3MudmFsdWUueQ0KDQogICAgICAgIHRyYW5zbGF0ZVgudmFsdWUgKz0gZGVsdGFYDQogICAgICAgIHRyYW5zbGF0ZVkudmFsdWUgKz0gZGVsdGFZDQoNCiAgICAgICAgbGFzdFRvdWNoUG9zLnZhbHVlID0gew0KICAgICAgICAgIHg6IGUudG91Y2hlc1swXS5jbGllbnRYLA0KICAgICAgICAgIHk6IGUudG91Y2hlc1swXS5jbGllbnRZDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAoZS50b3VjaGVzLmxlbmd0aCA9PT0gMikgew0KICAgICAgICAvLyDlj4zmjIfnvKnmlL4NCiAgICAgICAgY29uc3QgZGlzdGFuY2UgPSBnZXREaXN0YW5jZShlLnRvdWNoZXNbMF0sIGUudG91Y2hlc1sxXSkNCiAgICAgICAgY29uc3QgY2VudGVyID0gZ2V0Q2VudGVyKGUudG91Y2hlc1swXSwgZS50b3VjaGVzWzFdKQ0KDQogICAgICAgIGlmIChsYXN0VG91Y2hEaXN0YW5jZS52YWx1ZSA+IDApIHsNCiAgICAgICAgICBjb25zdCBzY2FsZUNoYW5nZSA9IGRpc3RhbmNlIC8gbGFzdFRvdWNoRGlzdGFuY2UudmFsdWUNCiAgICAgICAgICBjb25zdCBuZXdTY2FsZSA9IE1hdGgubWF4KDAuNSwgTWF0aC5taW4oMywgc2NhbGUudmFsdWUgKiBzY2FsZUNoYW5nZSkpDQoNCiAgICAgICAgICAvLyDku6Xop6bmkbjkuK3lv4PkuLrnvKnmlL7kuK3lv4MNCiAgICAgICAgICBjb25zdCByZWN0ID0gcGRmQ29udGFpbmVyLnZhbHVlLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpDQogICAgICAgICAgY29uc3QgY2VudGVyWCA9IGNlbnRlci54IC0gcmVjdC5sZWZ0DQogICAgICAgICAgY29uc3QgY2VudGVyWSA9IGNlbnRlci55IC0gcmVjdC50b3ANCg0KICAgICAgICAgIGNvbnN0IHNjYWxlRGlmZiA9IG5ld1NjYWxlIC0gc2NhbGUudmFsdWUNCiAgICAgICAgICB0cmFuc2xhdGVYLnZhbHVlIC09IChjZW50ZXJYIC0gcmVjdC53aWR0aCAvIDIpICogc2NhbGVEaWZmIC8gc2NhbGUudmFsdWUNCiAgICAgICAgICB0cmFuc2xhdGVZLnZhbHVlIC09IChjZW50ZXJZIC0gcmVjdC5oZWlnaHQgLyAyKSAqIHNjYWxlRGlmZiAvIHNjYWxlLnZhbHVlDQoNCiAgICAgICAgICBzY2FsZS52YWx1ZSA9IG5ld1NjYWxlDQogICAgICAgIH0NCg0KICAgICAgICBsYXN0VG91Y2hEaXN0YW5jZS52YWx1ZSA9IGRpc3RhbmNlDQogICAgICAgIGxhc3RUb3VjaENlbnRlci52YWx1ZSA9IGNlbnRlcg0KICAgICAgfQ0KICAgIH0NCg0KICAgIC8vIOinpuaRuOe7k+adnw0KICAgIGNvbnN0IGhhbmRsZVRvdWNoRW5kID0gKGUpID0+IHsNCiAgICAgIGUucHJldmVudERlZmF1bHQoKQ0KICAgICAgaXNEcmFnZ2luZy52YWx1ZSA9IGZhbHNlDQogICAgICBsYXN0VG91Y2hEaXN0YW5jZS52YWx1ZSA9IDANCiAgICB9DQoNCiAgICAvLyDpvKDmoIfmu5rova7nvKnmlL4NCiAgICBjb25zdCBoYW5kbGVXaGVlbCA9IChlKSA9PiB7DQogICAgICBlLnByZXZlbnREZWZhdWx0KCkNCg0KICAgICAgY29uc3QgcmVjdCA9IHBkZkNvbnRhaW5lci52YWx1ZS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKQ0KICAgICAgY29uc3QgY2VudGVyWCA9IGUuY2xpZW50WCAtIHJlY3QubGVmdA0KICAgICAgY29uc3QgY2VudGVyWSA9IGUuY2xpZW50WSAtIHJlY3QudG9wDQoNCiAgICAgIGNvbnN0IHNjYWxlQ2hhbmdlID0gZS5kZWx0YVkgPiAwID8gMC45IDogMS4xDQogICAgICBjb25zdCBuZXdTY2FsZSA9IE1hdGgubWF4KDAuNSwgTWF0aC5taW4oMywgc2NhbGUudmFsdWUgKiBzY2FsZUNoYW5nZSkpDQoNCiAgICAgIGNvbnN0IHNjYWxlRGlmZiA9IG5ld1NjYWxlIC0gc2NhbGUudmFsdWUNCiAgICAgIHRyYW5zbGF0ZVgudmFsdWUgLT0gKGNlbnRlclggLSByZWN0LndpZHRoIC8gMikgKiBzY2FsZURpZmYgLyBzY2FsZS52YWx1ZQ0KICAgICAgdHJhbnNsYXRlWS52YWx1ZSAtPSAoY2VudGVyWSAtIHJlY3QuaGVpZ2h0IC8gMikgKiBzY2FsZURpZmYgLyBzY2FsZS52YWx1ZQ0KDQogICAgICBzY2FsZS52YWx1ZSA9IG5ld1NjYWxlDQogICAgfQ0KDQogICAgcmV0dXJuIHsNCiAgICAgIHBkZlVybCwNCiAgICAgIG9uQ2xpY2tMZWZ0LA0KICAgICAgdGl0bGUsDQogICAgICBoYXNBcGksDQogICAgICBsb2FkaW5nLA0KICAgICAgcGRmQ29udGFpbmVyLA0KICAgICAgcGRmQ2FudmFzLA0KICAgICAgc2NhbGUsDQogICAgICBjYW52YXNTdHlsZSwNCiAgICAgIHpvb21JbiwNCiAgICAgIHpvb21PdXQsDQogICAgICByZXNldFpvb20sDQogICAgICBoYW5kbGVUb3VjaFN0YXJ0LA0KICAgICAgaGFuZGxlVG91Y2hNb3ZlLA0KICAgICAgaGFuZGxlVG91Y2hFbmQsDQogICAgICBoYW5kbGVXaGVlbA0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue"], "names": [], "mappings": ";AAgCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;IAEvC,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC;;IAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;;IAEA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV;MACF,CAAC;IACH;;IAEA,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB;IACF;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACpB;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7C;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACrB;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACpC;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACzC;IACF;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1B,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D;IACF;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9C,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAErE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAElC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAExE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IAC5B;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/pdfFilePreview/pdfFilePreview.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"pdfFilePreview\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" :title=\"title\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <template v-if=\"pdfUrl\">\r\n      <!-- PDF 容器，支持手势缩放 -->\r\n      <div ref=\"pdfContainer\" class=\"pdf-container\" @touchstart=\"handleTouchStart\" @touchmove=\"handleTouchMove\"\r\n        @touchend=\"handleTouchEnd\" @wheel=\"handleWheel\">\r\n        <canvas ref=\"pdfCanvas\" class=\"pdf-canvas\" :style=\"canvasStyle\"></canvas>\r\n\r\n        <!-- 加载状态 -->\r\n        <div v-if=\"loading\" class=\"loading-overlay\">\r\n          <van-loading color=\"#1989fa\" />\r\n          <div>PDF加载中...</div>\r\n        </div>\r\n\r\n        <!-- 控制按钮 -->\r\n        <div class=\"pdf-controls\">\r\n          <van-button size=\"small\" @click=\"zoomOut\">-</van-button>\r\n          <span class=\"zoom-text\">{{ Math.round(scale * 100) }}%</span>\r\n          <van-button size=\"small\" @click=\"zoomIn\">+</van-button>\r\n          <van-button size=\"small\" @click=\"resetZoom\">重置</van-button>\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <template v-else>\r\n      <div style=\"color:#fff;text-align:center;padding-top:40vh;\">未获取到PDF地址</div>\r\n    </template>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { computed, ref, onMounted, nextTick } from 'vue'\r\nimport { NavBar, Sticky, Button, Loading } from 'vant'\r\n\r\nexport default {\r\n  name: 'pdfFilePreview',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Button.name]: Button,\r\n    [Loading.name]: Loading\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const pdfUrl = computed(() => route.query.url)\r\n    const hasApi = ref(true)\r\n    const title = ref(route.query.title)\r\n    const loading = ref(false)\r\n\r\n    // PDF 相关状态\r\n    const pdfContainer = ref(null)\r\n    const pdfCanvas = ref(null)\r\n    const scale = ref(1)\r\n    const translateX = ref(0)\r\n    const translateY = ref(0)\r\n    const pdfDoc = ref(null)\r\n    const currentPage = ref(1)\r\n\r\n    // 触摸相关状态\r\n    const lastTouchDistance = ref(0)\r\n    const lastTouchCenter = ref({ x: 0, y: 0 })\r\n    const isDragging = ref(false)\r\n    const lastTouchPos = ref({ x: 0, y: 0 })\r\n\r\n    // 计算样式\r\n    const canvasStyle = computed(() => ({\r\n      transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value})`,\r\n      transformOrigin: 'center center',\r\n      transition: isDragging.value ? 'none' : 'transform 0.3s ease'\r\n    }))\r\n\r\n    if (title.value) {\r\n      document.title = title.value\r\n    }\r\n\r\n    // 加载 PDF.js\r\n    const loadPDFJS = async () => {\r\n      if (window.pdfjsLib) return\r\n\r\n      // 动态加载 PDF.js\r\n      const script = document.createElement('script')\r\n      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'\r\n      document.head.appendChild(script)\r\n\r\n      return new Promise((resolve) => {\r\n        script.onload = () => {\r\n          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'\r\n          resolve()\r\n        }\r\n      })\r\n    }\r\n\r\n    // 渲染 PDF\r\n    const renderPDF = async () => {\r\n      if (!pdfUrl.value || !pdfCanvas.value) return\r\n\r\n      loading.value = true\r\n\r\n      try {\r\n        await loadPDFJS()\r\n\r\n        const loadingTask = window.pdfjsLib.getDocument(pdfUrl.value)\r\n        pdfDoc.value = await loadingTask.promise\r\n\r\n        await renderPage(currentPage.value)\r\n      } catch (error) {\r\n        console.error('PDF 加载失败:', error)\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n\r\n    // 渲染页面\r\n    const renderPage = async (pageNum) => {\r\n      if (!pdfDoc.value || !pdfCanvas.value) return\r\n\r\n      const page = await pdfDoc.value.getPage(pageNum)\r\n      const canvas = pdfCanvas.value\r\n      const context = canvas.getContext('2d')\r\n\r\n      const viewport = page.getViewport({ scale: 1.5 })\r\n      canvas.height = viewport.height\r\n      canvas.width = viewport.width\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: viewport\r\n      }\r\n\r\n      await page.render(renderContext).promise\r\n    }\r\n\r\n    onMounted(async () => {\r\n      if (typeof (api) === 'undefined') {\r\n        hasApi.value = false\r\n      } else {\r\n        hasApi.value = true\r\n      }\r\n\r\n      await nextTick()\r\n      if (pdfUrl.value) {\r\n        await renderPDF()\r\n      }\r\n    })\r\n\r\n    const onClickLeft = () => {\r\n      if (typeof (api) === 'undefined') return history.back()\r\n      // eslint-disable-next-line no-undef\r\n      api.closeWin()\r\n    }\r\n\r\n    // 缩放控制\r\n    const zoomIn = () => {\r\n      scale.value = Math.min(scale.value * 1.2, 3)\r\n    }\r\n\r\n    const zoomOut = () => {\r\n      scale.value = Math.max(scale.value / 1.2, 0.5)\r\n    }\r\n\r\n    const resetZoom = () => {\r\n      scale.value = 1\r\n      translateX.value = 0\r\n      translateY.value = 0\r\n    }\r\n\r\n    // 获取两点间距离\r\n    const getDistance = (touch1, touch2) => {\r\n      const dx = touch1.clientX - touch2.clientX\r\n      const dy = touch1.clientY - touch2.clientY\r\n      return Math.sqrt(dx * dx + dy * dy)\r\n    }\r\n\r\n    // 获取两点中心\r\n    const getCenter = (touch1, touch2) => {\r\n      return {\r\n        x: (touch1.clientX + touch2.clientX) / 2,\r\n        y: (touch1.clientY + touch2.clientY) / 2\r\n      }\r\n    }\r\n\r\n    // 触摸开始\r\n    const handleTouchStart = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1) {\r\n        // 单指拖拽\r\n        isDragging.value = true\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        isDragging.value = false\r\n        lastTouchDistance.value = getDistance(e.touches[0], e.touches[1])\r\n        lastTouchCenter.value = getCenter(e.touches[0], e.touches[1])\r\n      }\r\n    }\r\n\r\n    // 触摸移动\r\n    const handleTouchMove = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1 && isDragging.value) {\r\n        // 单指拖拽\r\n        const deltaX = e.touches[0].clientX - lastTouchPos.value.x\r\n        const deltaY = e.touches[0].clientY - lastTouchPos.value.y\r\n\r\n        translateX.value += deltaX\r\n        translateY.value += deltaY\r\n\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        const distance = getDistance(e.touches[0], e.touches[1])\r\n        const center = getCenter(e.touches[0], e.touches[1])\r\n\r\n        if (lastTouchDistance.value > 0) {\r\n          const scaleChange = distance / lastTouchDistance.value\r\n          const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n          // 以触摸中心为缩放中心\r\n          const rect = pdfContainer.value.getBoundingClientRect()\r\n          const centerX = center.x - rect.left\r\n          const centerY = center.y - rect.top\r\n\r\n          const scaleDiff = newScale - scale.value\r\n          translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n          translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n          scale.value = newScale\r\n        }\r\n\r\n        lastTouchDistance.value = distance\r\n        lastTouchCenter.value = center\r\n      }\r\n    }\r\n\r\n    // 触摸结束\r\n    const handleTouchEnd = (e) => {\r\n      e.preventDefault()\r\n      isDragging.value = false\r\n      lastTouchDistance.value = 0\r\n    }\r\n\r\n    // 鼠标滚轮缩放\r\n    const handleWheel = (e) => {\r\n      e.preventDefault()\r\n\r\n      const rect = pdfContainer.value.getBoundingClientRect()\r\n      const centerX = e.clientX - rect.left\r\n      const centerY = e.clientY - rect.top\r\n\r\n      const scaleChange = e.deltaY > 0 ? 0.9 : 1.1\r\n      const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n      const scaleDiff = newScale - scale.value\r\n      translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n      translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n      scale.value = newScale\r\n    }\r\n\r\n    return {\r\n      pdfUrl,\r\n      onClickLeft,\r\n      title,\r\n      hasApi,\r\n      loading,\r\n      pdfContainer,\r\n      pdfCanvas,\r\n      scale,\r\n      canvasStyle,\r\n      zoomIn,\r\n      zoomOut,\r\n      resetZoom,\r\n      handleTouchStart,\r\n      handleTouchMove,\r\n      handleTouchEnd,\r\n      handleWheel\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: simplified;\r\n  }\r\n}\r\n\r\n.pdfFilePreview {\r\n  width: 100%;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background: #000;\r\n}\r\n</style>\r\n"]}]}