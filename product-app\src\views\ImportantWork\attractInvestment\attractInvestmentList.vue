<template>
  <div class="attractInvestmentList">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="招商引资情况统计" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
        <ul class="vue_newslist_box">
          <div class="investment_card" v-for="(item, index) in dataList" :key="index" @click="openDetails(item)">
            <div class="investment_title">{{ item.title }}</div>
            <div class="investment_bottom">
              <span class="investment_date">{{ dayjs(item.publishDate).format('YYYY-MM-DD') }}</span>
              <span class="investment_type_btn">{{ item.type == '1' ? '外出拜访' : item.type == '2' ? '在青接待' : item.type ==
                '3' ? '自主举办' : '' }}</span>
            </div>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs } from 'vue'
import { NavBar, Sticky, Tag } from 'vant'
export default {
  name: 'attractInvestmentList',
  components: {
    [Tag.name]: Tag,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      noticeList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        noticeList()
      }, 520)
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      noticeList()
    }
    // 列表请求
    const noticeList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: data.pageNo,
        pageSize: 10,
        columnId: route.query.columnId,
        backupTwo: route.query.secondaryColumn || ''
      })
      var { data: list, total } = res
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      router.push({ name: 'attractInvestmentDetails', query: { id: row.id } })
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), dayjs, search, onRefresh, onLoad, openDetails, $general, onClickLeft }
  }
}
</script>
<style lang="less">
.attractInvestmentList {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .investment_card {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;

    .investment_title {
      font-family: Source Han Serif SC, Source Han Serif SC;
      // font-weight: bold;
      font-size: 18px;
      color: #333333;
      margin-bottom: 12px;
    }

    .investment_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .investment_date {
        font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
        font-size: 12px;
        color: #666666;
      }

      .investment_type_btn {
        background: rgba(0, 122, 255, 0.12);
        border: 1px solid #007AFF;
        border-radius: 2px;
        font-family: Source Han Serif SC, Source Han Serif SC;
        font-weight: 500;
        font-size: 12px;
        color: #007AFF;
        padding: 3px;
      }
    }
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
