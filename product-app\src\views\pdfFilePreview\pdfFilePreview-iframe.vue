<template>
  <div class="pdfFilePreview">
    <van-sticky>
      <van-nav-bar v-if="hasApi" :title="title" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <template v-if="pdfUrl">
      <!-- 使用 object 标签替代 iframe，支持更好的缩放 -->
      <div class="pdf-wrapper">
        <object 
          :data="pdfUrl" 
          type="application/pdf" 
          class="pdf-object"
        >
          <iframe 
            :src="pdfUrl" 
            class="pdf-iframe"
          ></iframe>
        </object>
        
        <!-- 添加手势提示 -->
        <div class="gesture-hint">
          <div class="hint-text">双指缩放 · 单指拖拽</div>
        </div>
      </div>
    </template>
    <template v-else>
      <div style="color:#fff;text-align:center;padding-top:40vh;">未获取到PDF地址</div>
    </template>
  </div>
</template>

<script>
import { useRoute } from 'vue-router'
import { computed, ref, onMounted } from 'vue'
import { NavBar, Sticky } from 'vant'

export default {
  name: 'pdfFilePreview',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const pdfUrl = computed(() => route.query.url)
    const hasApi = ref(true)
    const title = ref(route.query.title)
    
    if (title.value) {
      document.title = title.value
    }

    onMounted(() => {
      if (typeof (api) === 'undefined') {
        hasApi.value = false
      } else {
        hasApi.value = true
      }
      
      // 设置 viewport 以支持缩放
      const viewport = document.querySelector('meta[name="viewport"]')
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=3.0, user-scalable=yes')
      } else {
        const meta = document.createElement('meta')
        meta.name = 'viewport'
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=3.0, user-scalable=yes'
        document.head.appendChild(meta)
      }
    })
    
    const onClickLeft = () => {
      if (typeof (api) === 'undefined') return history.back()
      // eslint-disable-next-line no-undef
      api.closeWin()
    }
    
    return { pdfUrl, onClickLeft, title, hasApi }
  }
}
</script>

<style lang="less">
.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}

.pdfFilePreview {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #000;
  
  /* 启用硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.pdf-wrapper {
  position: relative;
  width: 100%;
  height: calc(100% - 35px);
  overflow: auto;
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
}

.pdf-object,
.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  
  /* 启用触摸缩放 */
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.gesture-hint {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 12px;
  border-radius: 16px;
  z-index: 10;
  animation: fadeInOut 3s ease-in-out;
  
  .hint-text {
    color: white;
    font-size: 12px;
    white-space: nowrap;
  }
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .pdf-wrapper {
    /* 允许缩放和滚动 */
    overflow: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .pdf-object,
  .pdf-iframe {
    /* 移动端触摸优化 */
    touch-action: pan-x pan-y pinch-zoom;
  }
}
</style>
