{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue?vue&type=template&id=8678c0f6", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue", "mtime": 1756714397355}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_van_sticky", "$setup", "has<PERSON><PERSON>", "_createBlock", "_component_van_nav_bar", "title", "onClickLeft", "pdfUrl", "_Fragment", "key", "_createCommentVNode", "_createElementVNode", "ref", "onTouchstart", "_cache", "args", "handleTouchStart", "onTouchmove", "handleTouchMove", "onTouchend", "handleTouchEnd", "onWheel", "handleWheel", "_normalizeStyle", "canvasStyle", "loading", "_hoisted_2", "_component_van_loading", "color", "_hoisted_3", "_component_van_button", "size", "onClick", "zoomOut", "_hoisted_4", "_toDisplayString", "Math", "round", "scale", "zoomIn", "resetZoom", "_hoisted_5"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"pdfFilePreview\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" :title=\"title\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <template v-if=\"pdfUrl\">\r\n      <!-- PDF 容器，支持手势缩放 -->\r\n      <div ref=\"pdfContainer\" class=\"pdf-container\" @touchstart=\"handleTouchStart\" @touchmove=\"handleTouchMove\"\r\n        @touchend=\"handleTouchEnd\" @wheel=\"handleWheel\">\r\n        <canvas ref=\"pdfCanvas\" class=\"pdf-canvas\" :style=\"canvasStyle\"></canvas>\r\n\r\n        <!-- 加载状态 -->\r\n        <div v-if=\"loading\" class=\"loading-overlay\">\r\n          <van-loading color=\"#1989fa\" />\r\n          <div>PDF加载中...</div>\r\n        </div>\r\n\r\n        <!-- 控制按钮 -->\r\n        <div class=\"pdf-controls\">\r\n          <van-button size=\"small\" @click=\"zoomOut\">-</van-button>\r\n          <span class=\"zoom-text\">{{ Math.round(scale * 100) }}%</span>\r\n          <van-button size=\"small\" @click=\"zoomIn\">+</van-button>\r\n          <van-button size=\"small\" @click=\"resetZoom\">重置</van-button>\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <template v-else>\r\n      <div style=\"color:#fff;text-align:center;padding-top:40vh;\">未获取到PDF地址</div>\r\n    </template>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { computed, ref, onMounted, nextTick } from 'vue'\r\nimport { NavBar, Sticky, Button, Loading } from 'vant'\r\n\r\nexport default {\r\n  name: 'pdfFilePreview',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Button.name]: Button,\r\n    [Loading.name]: Loading\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const pdfUrl = computed(() => route.query.url)\r\n    const hasApi = ref(true)\r\n    const title = ref(route.query.title)\r\n    const loading = ref(false)\r\n\r\n    // PDF 相关状态\r\n    const pdfContainer = ref(null)\r\n    const pdfCanvas = ref(null)\r\n    const scale = ref(1)\r\n    const translateX = ref(0)\r\n    const translateY = ref(0)\r\n    const pdfDoc = ref(null)\r\n    const currentPage = ref(1)\r\n\r\n    // 触摸相关状态\r\n    const lastTouchDistance = ref(0)\r\n    const lastTouchCenter = ref({ x: 0, y: 0 })\r\n    const isDragging = ref(false)\r\n    const lastTouchPos = ref({ x: 0, y: 0 })\r\n\r\n    // 计算样式\r\n    const canvasStyle = computed(() => ({\r\n      transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value})`,\r\n      transformOrigin: 'center center',\r\n      transition: isDragging.value ? 'none' : 'transform 0.3s ease'\r\n    }))\r\n\r\n    if (title.value) {\r\n      document.title = title.value\r\n    }\r\n\r\n    // 加载 PDF.js\r\n    const loadPDFJS = async () => {\r\n      if (window.pdfjsLib) return\r\n\r\n      // 动态加载 PDF.js\r\n      const script = document.createElement('script')\r\n      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'\r\n      document.head.appendChild(script)\r\n\r\n      return new Promise((resolve) => {\r\n        script.onload = () => {\r\n          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'\r\n          resolve()\r\n        }\r\n      })\r\n    }\r\n\r\n    // 渲染 PDF\r\n    const renderPDF = async () => {\r\n      if (!pdfUrl.value || !pdfCanvas.value) return\r\n\r\n      loading.value = true\r\n\r\n      try {\r\n        await loadPDFJS()\r\n\r\n        const loadingTask = window.pdfjsLib.getDocument(pdfUrl.value)\r\n        pdfDoc.value = await loadingTask.promise\r\n\r\n        await renderPage(currentPage.value)\r\n      } catch (error) {\r\n        console.error('PDF 加载失败:', error)\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n\r\n    // 渲染页面\r\n    const renderPage = async (pageNum) => {\r\n      if (!pdfDoc.value || !pdfCanvas.value) return\r\n\r\n      const page = await pdfDoc.value.getPage(pageNum)\r\n      const canvas = pdfCanvas.value\r\n      const context = canvas.getContext('2d')\r\n\r\n      const viewport = page.getViewport({ scale: 1.5 })\r\n      canvas.height = viewport.height\r\n      canvas.width = viewport.width\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: viewport\r\n      }\r\n\r\n      await page.render(renderContext).promise\r\n    }\r\n\r\n    onMounted(async () => {\r\n      if (typeof (api) === 'undefined') {\r\n        hasApi.value = false\r\n      } else {\r\n        hasApi.value = true\r\n      }\r\n\r\n      await nextTick()\r\n      if (pdfUrl.value) {\r\n        await renderPDF()\r\n      }\r\n    })\r\n\r\n    const onClickLeft = () => {\r\n      if (typeof (api) === 'undefined') return history.back()\r\n      // eslint-disable-next-line no-undef\r\n      api.closeWin()\r\n    }\r\n\r\n    // 缩放控制\r\n    const zoomIn = () => {\r\n      scale.value = Math.min(scale.value * 1.2, 3)\r\n    }\r\n\r\n    const zoomOut = () => {\r\n      scale.value = Math.max(scale.value / 1.2, 0.5)\r\n    }\r\n\r\n    const resetZoom = () => {\r\n      scale.value = 1\r\n      translateX.value = 0\r\n      translateY.value = 0\r\n    }\r\n\r\n    // 获取两点间距离\r\n    const getDistance = (touch1, touch2) => {\r\n      const dx = touch1.clientX - touch2.clientX\r\n      const dy = touch1.clientY - touch2.clientY\r\n      return Math.sqrt(dx * dx + dy * dy)\r\n    }\r\n\r\n    // 获取两点中心\r\n    const getCenter = (touch1, touch2) => {\r\n      return {\r\n        x: (touch1.clientX + touch2.clientX) / 2,\r\n        y: (touch1.clientY + touch2.clientY) / 2\r\n      }\r\n    }\r\n\r\n    // 触摸开始\r\n    const handleTouchStart = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1) {\r\n        // 单指拖拽\r\n        isDragging.value = true\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        isDragging.value = false\r\n        lastTouchDistance.value = getDistance(e.touches[0], e.touches[1])\r\n        lastTouchCenter.value = getCenter(e.touches[0], e.touches[1])\r\n      }\r\n    }\r\n\r\n    // 触摸移动\r\n    const handleTouchMove = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1 && isDragging.value) {\r\n        // 单指拖拽\r\n        const deltaX = e.touches[0].clientX - lastTouchPos.value.x\r\n        const deltaY = e.touches[0].clientY - lastTouchPos.value.y\r\n\r\n        translateX.value += deltaX\r\n        translateY.value += deltaY\r\n\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        const distance = getDistance(e.touches[0], e.touches[1])\r\n        const center = getCenter(e.touches[0], e.touches[1])\r\n\r\n        if (lastTouchDistance.value > 0) {\r\n          const scaleChange = distance / lastTouchDistance.value\r\n          const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n          // 以触摸中心为缩放中心\r\n          const rect = pdfContainer.value.getBoundingClientRect()\r\n          const centerX = center.x - rect.left\r\n          const centerY = center.y - rect.top\r\n\r\n          const scaleDiff = newScale - scale.value\r\n          translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n          translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n          scale.value = newScale\r\n        }\r\n\r\n        lastTouchDistance.value = distance\r\n        lastTouchCenter.value = center\r\n      }\r\n    }\r\n\r\n    // 触摸结束\r\n    const handleTouchEnd = (e) => {\r\n      e.preventDefault()\r\n      isDragging.value = false\r\n      lastTouchDistance.value = 0\r\n    }\r\n\r\n    // 鼠标滚轮缩放\r\n    const handleWheel = (e) => {\r\n      e.preventDefault()\r\n\r\n      const rect = pdfContainer.value.getBoundingClientRect()\r\n      const centerX = e.clientX - rect.left\r\n      const centerY = e.clientY - rect.top\r\n\r\n      const scaleChange = e.deltaY > 0 ? 0.9 : 1.1\r\n      const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n      const scaleDiff = newScale - scale.value\r\n      translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n      translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n      scale.value = newScale\r\n    }\r\n\r\n    return {\r\n      pdfUrl,\r\n      onClickLeft,\r\n      title,\r\n      hasApi,\r\n      loading,\r\n      pdfContainer,\r\n      pdfCanvas,\r\n      scale,\r\n      canvasStyle,\r\n      zoomIn,\r\n      zoomOut,\r\n      resetZoom,\r\n      handleTouchStart,\r\n      handleTouchMove,\r\n      handleTouchEnd,\r\n      handleWheel\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: simplified;\r\n  }\r\n}\r\n\r\n.pdfFilePreview {\r\n  width: 100%;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background: #000;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;;EAWDA,KAAK,EAAC;;;EAMrBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAW;;;EAOtBC,KAAsD,EAAtD;IAAA;IAAA;IAAA;EAAA;;;;;;;uBA1BTC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJC,YAAA,CAEaC,qBAAA;sBADX,MAA8F,CAA3EC,MAAA,CAAAC,MAAM,I,cAAzBC,YAAA,CAA8FC,sBAAA;;MAAlEC,KAAK,EAAEJ,MAAA,CAAAI,KAAK;MAAE,WAAS,EAAC,EAAE;MAAC,YAAU,EAAV,EAAU;MAAEC,WAAU,EAAEL,MAAA,CAAAK;;;MAEjEL,MAAA,CAAAM,MAAM,I,cAAtBV,mBAAA,CAoBWW,SAAA;IAAAC,GAAA;EAAA,IAnBTC,mBAAA,mBAAsB,EACtBC,mBAAA,CAiBM;IAjBDC,GAAG,EAAC,cAAc;IAACjB,KAAK,EAAC,eAAe;IAAEkB,YAAU,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEd,MAAA,CAAAe,gBAAA,IAAAf,MAAA,CAAAe,gBAAA,IAAAD,IAAA,CAAgB;IAAGE,WAAS,EAAAH,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEd,MAAA,CAAAiB,eAAA,IAAAjB,MAAA,CAAAiB,eAAA,IAAAH,IAAA,CAAe;IACrGI,UAAQ,EAAAL,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEd,MAAA,CAAAmB,cAAA,IAAAnB,MAAA,CAAAmB,cAAA,IAAAL,IAAA,CAAc;IAAGM,OAAK,EAAAP,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEd,MAAA,CAAAqB,WAAA,IAAArB,MAAA,CAAAqB,WAAA,IAAAP,IAAA,CAAW;MAC9CJ,mBAAA,CAAyE;IAAjEC,GAAG,EAAC,WAAW;IAACjB,KAAK,EAAC,YAAY;IAAEC,KAAK,EAAA2B,eAAA,CAAEtB,MAAA,CAAAuB,WAAW;2BAE9Dd,mBAAA,UAAa,EACFT,MAAA,CAAAwB,OAAO,I,cAAlB5B,mBAAA,CAGM,OAHN6B,UAGM,GAFJ3B,YAAA,CAA+B4B,sBAAA;IAAlBC,KAAK,EAAC;EAAS,I,0BAC5BjB,mBAAA,CAAoB,aAAf,WAAS,qB,wCAGhBD,mBAAA,UAAa,EACbC,mBAAA,CAKM,OALNkB,UAKM,GAJJ9B,YAAA,CAAwD+B,qBAAA;IAA5CC,IAAI,EAAC,OAAO;IAAEC,OAAK,EAAE/B,MAAA,CAAAgC;;sBAAS,MAACnB,MAAA,QAAAA,MAAA,O,iBAAD,GAAC,E;;kCAC3CH,mBAAA,CAA6D,QAA7DuB,UAA6D,EAAAC,gBAAA,CAAlCC,IAAI,CAACC,KAAK,CAACpC,MAAA,CAAAqC,KAAK,WAAU,GAAC,iBACtDvC,YAAA,CAAuD+B,qBAAA;IAA3CC,IAAI,EAAC,OAAO;IAAEC,OAAK,EAAE/B,MAAA,CAAAsC;;sBAAQ,MAACzB,MAAA,QAAAA,MAAA,O,iBAAD,GAAC,E;;kCAC1Cf,YAAA,CAA2D+B,qBAAA;IAA/CC,IAAI,EAAC,OAAO;IAAEC,OAAK,EAAE/B,MAAA,CAAAuC;;sBAAW,MAAE1B,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;yHAKlDjB,mBAAA,CAA2E,OAA3E4C,UAA2E,EAAf,WAAS,G", "ignoreList": []}]}