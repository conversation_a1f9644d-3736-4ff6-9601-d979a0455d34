{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue", "mtime": 1756714397355}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["useRoute", "computed", "ref", "onMounted", "nextTick", "name", "components", "_NavBar", "_Sticky", "_<PERSON><PERSON>", "_Loading", "setup", "route", "pdfUrl", "query", "url", "has<PERSON><PERSON>", "title", "loading", "pdfContainer", "pdfCanvas", "scale", "translateX", "translateY", "pdfDoc", "currentPage", "lastTouchDistance", "lastTouchCenter", "x", "y", "isDragging", "lastTouchPos", "canvasStyle", "transform", "value", "transform<PERSON><PERSON>in", "transition", "document", "loadPDFJS", "window", "pdfjsLib", "script", "createElement", "src", "head", "append<PERSON><PERSON><PERSON>", "Promise", "resolve", "onload", "GlobalWorkerOptions", "workerSrc", "renderPDF", "loadingTask", "getDocument", "promise", "renderPage", "error", "console", "pageNum", "page", "getPage", "canvas", "context", "getContext", "viewport", "getViewport", "height", "width", "renderContext", "canvasContext", "render", "api", "onClickLeft", "history", "back", "closeWin", "zoomIn", "Math", "min", "zoomOut", "max", "resetZoom", "getDistance", "touch1", "touch2", "dx", "clientX", "dy", "clientY", "sqrt", "getCenter", "handleTouchStart", "e", "preventDefault", "touches", "length", "handleTouchMove", "deltaX", "deltaY", "distance", "center", "scaleChange", "newScale", "rect", "getBoundingClientRect", "centerX", "left", "centerY", "top", "scaleDiff", "handleTouchEnd", "handleWheel"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue"], "sourcesContent": ["<template>\r\n  <div class=\"pdfFilePreview\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" :title=\"title\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <template v-if=\"pdfUrl\">\r\n      <!-- PDF 容器，支持手势缩放 -->\r\n      <div ref=\"pdfContainer\" class=\"pdf-container\" @touchstart=\"handleTouchStart\" @touchmove=\"handleTouchMove\"\r\n        @touchend=\"handleTouchEnd\" @wheel=\"handleWheel\">\r\n        <canvas ref=\"pdfCanvas\" class=\"pdf-canvas\" :style=\"canvasStyle\"></canvas>\r\n\r\n        <!-- 加载状态 -->\r\n        <div v-if=\"loading\" class=\"loading-overlay\">\r\n          <van-loading color=\"#1989fa\" />\r\n          <div>PDF加载中...</div>\r\n        </div>\r\n\r\n        <!-- 控制按钮 -->\r\n        <div class=\"pdf-controls\">\r\n          <van-button size=\"small\" @click=\"zoomOut\">-</van-button>\r\n          <span class=\"zoom-text\">{{ Math.round(scale * 100) }}%</span>\r\n          <van-button size=\"small\" @click=\"zoomIn\">+</van-button>\r\n          <van-button size=\"small\" @click=\"resetZoom\">重置</van-button>\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <template v-else>\r\n      <div style=\"color:#fff;text-align:center;padding-top:40vh;\">未获取到PDF地址</div>\r\n    </template>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { computed, ref, onMounted, nextTick } from 'vue'\r\nimport { NavBar, Sticky, Button, Loading } from 'vant'\r\n\r\nexport default {\r\n  name: 'pdfFilePreview',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Button.name]: Button,\r\n    [Loading.name]: Loading\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const pdfUrl = computed(() => route.query.url)\r\n    const hasApi = ref(true)\r\n    const title = ref(route.query.title)\r\n    const loading = ref(false)\r\n\r\n    // PDF 相关状态\r\n    const pdfContainer = ref(null)\r\n    const pdfCanvas = ref(null)\r\n    const scale = ref(1)\r\n    const translateX = ref(0)\r\n    const translateY = ref(0)\r\n    const pdfDoc = ref(null)\r\n    const currentPage = ref(1)\r\n\r\n    // 触摸相关状态\r\n    const lastTouchDistance = ref(0)\r\n    const lastTouchCenter = ref({ x: 0, y: 0 })\r\n    const isDragging = ref(false)\r\n    const lastTouchPos = ref({ x: 0, y: 0 })\r\n\r\n    // 计算样式\r\n    const canvasStyle = computed(() => ({\r\n      transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value})`,\r\n      transformOrigin: 'center center',\r\n      transition: isDragging.value ? 'none' : 'transform 0.3s ease'\r\n    }))\r\n\r\n    if (title.value) {\r\n      document.title = title.value\r\n    }\r\n\r\n    // 加载 PDF.js\r\n    const loadPDFJS = async () => {\r\n      if (window.pdfjsLib) return\r\n\r\n      // 动态加载 PDF.js\r\n      const script = document.createElement('script')\r\n      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'\r\n      document.head.appendChild(script)\r\n\r\n      return new Promise((resolve) => {\r\n        script.onload = () => {\r\n          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'\r\n          resolve()\r\n        }\r\n      })\r\n    }\r\n\r\n    // 渲染 PDF\r\n    const renderPDF = async () => {\r\n      if (!pdfUrl.value || !pdfCanvas.value) return\r\n\r\n      loading.value = true\r\n\r\n      try {\r\n        await loadPDFJS()\r\n\r\n        const loadingTask = window.pdfjsLib.getDocument(pdfUrl.value)\r\n        pdfDoc.value = await loadingTask.promise\r\n\r\n        await renderPage(currentPage.value)\r\n      } catch (error) {\r\n        console.error('PDF 加载失败:', error)\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n\r\n    // 渲染页面\r\n    const renderPage = async (pageNum) => {\r\n      if (!pdfDoc.value || !pdfCanvas.value) return\r\n\r\n      const page = await pdfDoc.value.getPage(pageNum)\r\n      const canvas = pdfCanvas.value\r\n      const context = canvas.getContext('2d')\r\n\r\n      const viewport = page.getViewport({ scale: 1.5 })\r\n      canvas.height = viewport.height\r\n      canvas.width = viewport.width\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: viewport\r\n      }\r\n\r\n      await page.render(renderContext).promise\r\n    }\r\n\r\n    onMounted(async () => {\r\n      if (typeof (api) === 'undefined') {\r\n        hasApi.value = false\r\n      } else {\r\n        hasApi.value = true\r\n      }\r\n\r\n      await nextTick()\r\n      if (pdfUrl.value) {\r\n        await renderPDF()\r\n      }\r\n    })\r\n\r\n    const onClickLeft = () => {\r\n      if (typeof (api) === 'undefined') return history.back()\r\n      // eslint-disable-next-line no-undef\r\n      api.closeWin()\r\n    }\r\n\r\n    // 缩放控制\r\n    const zoomIn = () => {\r\n      scale.value = Math.min(scale.value * 1.2, 3)\r\n    }\r\n\r\n    const zoomOut = () => {\r\n      scale.value = Math.max(scale.value / 1.2, 0.5)\r\n    }\r\n\r\n    const resetZoom = () => {\r\n      scale.value = 1\r\n      translateX.value = 0\r\n      translateY.value = 0\r\n    }\r\n\r\n    // 获取两点间距离\r\n    const getDistance = (touch1, touch2) => {\r\n      const dx = touch1.clientX - touch2.clientX\r\n      const dy = touch1.clientY - touch2.clientY\r\n      return Math.sqrt(dx * dx + dy * dy)\r\n    }\r\n\r\n    // 获取两点中心\r\n    const getCenter = (touch1, touch2) => {\r\n      return {\r\n        x: (touch1.clientX + touch2.clientX) / 2,\r\n        y: (touch1.clientY + touch2.clientY) / 2\r\n      }\r\n    }\r\n\r\n    // 触摸开始\r\n    const handleTouchStart = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1) {\r\n        // 单指拖拽\r\n        isDragging.value = true\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        isDragging.value = false\r\n        lastTouchDistance.value = getDistance(e.touches[0], e.touches[1])\r\n        lastTouchCenter.value = getCenter(e.touches[0], e.touches[1])\r\n      }\r\n    }\r\n\r\n    // 触摸移动\r\n    const handleTouchMove = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1 && isDragging.value) {\r\n        // 单指拖拽\r\n        const deltaX = e.touches[0].clientX - lastTouchPos.value.x\r\n        const deltaY = e.touches[0].clientY - lastTouchPos.value.y\r\n\r\n        translateX.value += deltaX\r\n        translateY.value += deltaY\r\n\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        const distance = getDistance(e.touches[0], e.touches[1])\r\n        const center = getCenter(e.touches[0], e.touches[1])\r\n\r\n        if (lastTouchDistance.value > 0) {\r\n          const scaleChange = distance / lastTouchDistance.value\r\n          const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n          // 以触摸中心为缩放中心\r\n          const rect = pdfContainer.value.getBoundingClientRect()\r\n          const centerX = center.x - rect.left\r\n          const centerY = center.y - rect.top\r\n\r\n          const scaleDiff = newScale - scale.value\r\n          translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n          translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n          scale.value = newScale\r\n        }\r\n\r\n        lastTouchDistance.value = distance\r\n        lastTouchCenter.value = center\r\n      }\r\n    }\r\n\r\n    // 触摸结束\r\n    const handleTouchEnd = (e) => {\r\n      e.preventDefault()\r\n      isDragging.value = false\r\n      lastTouchDistance.value = 0\r\n    }\r\n\r\n    // 鼠标滚轮缩放\r\n    const handleWheel = (e) => {\r\n      e.preventDefault()\r\n\r\n      const rect = pdfContainer.value.getBoundingClientRect()\r\n      const centerX = e.clientX - rect.left\r\n      const centerY = e.clientY - rect.top\r\n\r\n      const scaleChange = e.deltaY > 0 ? 0.9 : 1.1\r\n      const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n      const scaleDiff = newScale - scale.value\r\n      translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n      translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n      scale.value = newScale\r\n    }\r\n\r\n    return {\r\n      pdfUrl,\r\n      onClickLeft,\r\n      title,\r\n      hasApi,\r\n      loading,\r\n      pdfContainer,\r\n      pdfCanvas,\r\n      scale,\r\n      canvasStyle,\r\n      zoomIn,\r\n      zoomOut,\r\n      resetZoom,\r\n      handleTouchStart,\r\n      handleTouchMove,\r\n      handleTouchEnd,\r\n      handleWheel\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: simplified;\r\n  }\r\n}\r\n\r\n.pdfFilePreview {\r\n  width: 100%;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background: #000;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAgCA,SAASA,QAAO,QAAS,YAAW;AACpC,SAASC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AAGvD,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACV,CAACC,OAAA,CAAOF,IAAI,GAAAE,OAAS;IACrB,CAACC,OAAA,CAAOH,IAAI,GAAAG,OAAS;IACrB,CAACC,OAAA,CAAOJ,IAAI,GAAAI,OAAS;IACrB,CAACC,QAAA,CAAQL,IAAI,GAAAK;EACf,CAAC;EACDC,KAAIA,CAAA,EAAK;IACP,MAAMC,KAAI,GAAIZ,QAAQ,CAAC;IACvB,MAAMa,MAAK,GAAIZ,QAAQ,CAAC,MAAMW,KAAK,CAACE,KAAK,CAACC,GAAG;IAC7C,MAAMC,MAAK,GAAId,GAAG,CAAC,IAAI;IACvB,MAAMe,KAAI,GAAIf,GAAG,CAACU,KAAK,CAACE,KAAK,CAACG,KAAK;IACnC,MAAMC,OAAM,GAAIhB,GAAG,CAAC,KAAK;;IAEzB;IACA,MAAMiB,YAAW,GAAIjB,GAAG,CAAC,IAAI;IAC7B,MAAMkB,SAAQ,GAAIlB,GAAG,CAAC,IAAI;IAC1B,MAAMmB,KAAI,GAAInB,GAAG,CAAC,CAAC;IACnB,MAAMoB,UAAS,GAAIpB,GAAG,CAAC,CAAC;IACxB,MAAMqB,UAAS,GAAIrB,GAAG,CAAC,CAAC;IACxB,MAAMsB,MAAK,GAAItB,GAAG,CAAC,IAAI;IACvB,MAAMuB,WAAU,GAAIvB,GAAG,CAAC,CAAC;;IAEzB;IACA,MAAMwB,iBAAgB,GAAIxB,GAAG,CAAC,CAAC;IAC/B,MAAMyB,eAAc,GAAIzB,GAAG,CAAC;MAAE0B,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC1C,MAAMC,UAAS,GAAI5B,GAAG,CAAC,KAAK;IAC5B,MAAM6B,YAAW,GAAI7B,GAAG,CAAC;MAAE0B,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;;IAEvC;IACA,MAAMG,WAAU,GAAI/B,QAAQ,CAAC,OAAO;MAClCgC,SAAS,EAAE,aAAaX,UAAU,CAACY,KAAK,OAAOX,UAAU,CAACW,KAAK,aAAab,KAAK,CAACa,KAAK,GAAG;MAC1FC,eAAe,EAAE,eAAe;MAChCC,UAAU,EAAEN,UAAU,CAACI,KAAI,GAAI,MAAK,GAAI;IAC1C,CAAC,CAAC;IAEF,IAAIjB,KAAK,CAACiB,KAAK,EAAE;MACfG,QAAQ,CAACpB,KAAI,GAAIA,KAAK,CAACiB,KAAI;IAC7B;;IAEA;IACA,MAAMI,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAIC,MAAM,CAACC,QAAQ,EAAE;;MAErB;MACA,MAAMC,MAAK,GAAIJ,QAAQ,CAACK,aAAa,CAAC,QAAQ;MAC9CD,MAAM,CAACE,GAAE,GAAI,mEAAkE;MAC/EN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACJ,MAAM;MAEhC,OAAO,IAAIK,OAAO,CAAEC,OAAO,IAAK;QAC9BN,MAAM,CAACO,MAAK,GAAI,MAAM;UACpBT,MAAM,CAACC,QAAQ,CAACS,mBAAmB,CAACC,SAAQ,GAAI,0EAAyE;UACzHH,OAAO,CAAC;QACV;MACF,CAAC;IACH;;IAEA;IACA,MAAMI,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACtC,MAAM,CAACqB,KAAI,IAAK,CAACd,SAAS,CAACc,KAAK,EAAE;MAEvChB,OAAO,CAACgB,KAAI,GAAI,IAAG;MAEnB,IAAI;QACF,MAAMI,SAAS,CAAC;QAEhB,MAAMc,WAAU,GAAIb,MAAM,CAACC,QAAQ,CAACa,WAAW,CAACxC,MAAM,CAACqB,KAAK;QAC5DV,MAAM,CAACU,KAAI,GAAI,MAAMkB,WAAW,CAACE,OAAM;QAEvC,MAAMC,UAAU,CAAC9B,WAAW,CAACS,KAAK;MACpC,EAAE,OAAOsB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC,UAAU;QACRtC,OAAO,CAACgB,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMqB,UAAS,GAAI,MAAOG,OAAO,IAAK;MACpC,IAAI,CAAClC,MAAM,CAACU,KAAI,IAAK,CAACd,SAAS,CAACc,KAAK,EAAE;MAEvC,MAAMyB,IAAG,GAAI,MAAMnC,MAAM,CAACU,KAAK,CAAC0B,OAAO,CAACF,OAAO;MAC/C,MAAMG,MAAK,GAAIzC,SAAS,CAACc,KAAI;MAC7B,MAAM4B,OAAM,GAAID,MAAM,CAACE,UAAU,CAAC,IAAI;MAEtC,MAAMC,QAAO,GAAIL,IAAI,CAACM,WAAW,CAAC;QAAE5C,KAAK,EAAE;MAAI,CAAC;MAChDwC,MAAM,CAACK,MAAK,GAAIF,QAAQ,CAACE,MAAK;MAC9BL,MAAM,CAACM,KAAI,GAAIH,QAAQ,CAACG,KAAI;MAE5B,MAAMC,aAAY,GAAI;QACpBC,aAAa,EAAEP,OAAO;QACtBE,QAAQ,EAAEA;MACZ;MAEA,MAAML,IAAI,CAACW,MAAM,CAACF,aAAa,CAAC,CAACd,OAAM;IACzC;IAEAnD,SAAS,CAAC,YAAY;MACpB,IAAI,OAAQoE,GAAG,KAAM,WAAW,EAAE;QAChCvD,MAAM,CAACkB,KAAI,GAAI,KAAI;MACrB,OAAO;QACLlB,MAAM,CAACkB,KAAI,GAAI,IAAG;MACpB;MAEA,MAAM9B,QAAQ,CAAC;MACf,IAAIS,MAAM,CAACqB,KAAK,EAAE;QAChB,MAAMiB,SAAS,CAAC;MAClB;IACF,CAAC;IAED,MAAMqB,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI,OAAQD,GAAG,KAAM,WAAW,EAAE,OAAOE,OAAO,CAACC,IAAI,CAAC;MACtD;MACAH,GAAG,CAACI,QAAQ,CAAC;IACf;;IAEA;IACA,MAAMC,MAAK,GAAIA,CAAA,KAAM;MACnBvD,KAAK,CAACa,KAAI,GAAI2C,IAAI,CAACC,GAAG,CAACzD,KAAK,CAACa,KAAI,GAAI,GAAG,EAAE,CAAC;IAC7C;IAEA,MAAM6C,OAAM,GAAIA,CAAA,KAAM;MACpB1D,KAAK,CAACa,KAAI,GAAI2C,IAAI,CAACG,GAAG,CAAC3D,KAAK,CAACa,KAAI,GAAI,GAAG,EAAE,GAAG;IAC/C;IAEA,MAAM+C,SAAQ,GAAIA,CAAA,KAAM;MACtB5D,KAAK,CAACa,KAAI,GAAI;MACdZ,UAAU,CAACY,KAAI,GAAI;MACnBX,UAAU,CAACW,KAAI,GAAI;IACrB;;IAEA;IACA,MAAMgD,WAAU,GAAIA,CAACC,MAAM,EAAEC,MAAM,KAAK;MACtC,MAAMC,EAAC,GAAIF,MAAM,CAACG,OAAM,GAAIF,MAAM,CAACE,OAAM;MACzC,MAAMC,EAAC,GAAIJ,MAAM,CAACK,OAAM,GAAIJ,MAAM,CAACI,OAAM;MACzC,OAAOX,IAAI,CAACY,IAAI,CAACJ,EAAC,GAAIA,EAAC,GAAIE,EAAC,GAAIA,EAAE;IACpC;;IAEA;IACA,MAAMG,SAAQ,GAAIA,CAACP,MAAM,EAAEC,MAAM,KAAK;MACpC,OAAO;QACLxD,CAAC,EAAE,CAACuD,MAAM,CAACG,OAAM,GAAIF,MAAM,CAACE,OAAO,IAAI,CAAC;QACxCzD,CAAC,EAAE,CAACsD,MAAM,CAACK,OAAM,GAAIJ,MAAM,CAACI,OAAO,IAAI;MACzC;IACF;;IAEA;IACA,MAAMG,gBAAe,GAAKC,CAAC,IAAK;MAC9BA,CAAC,CAACC,cAAc,CAAC;MAEjB,IAAID,CAAC,CAACE,OAAO,CAACC,MAAK,KAAM,CAAC,EAAE;QAC1B;QACAjE,UAAU,CAACI,KAAI,GAAI,IAAG;QACtBH,YAAY,CAACG,KAAI,GAAI;UACnBN,CAAC,EAAEgE,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAACR,OAAO;UACvBzD,CAAC,EAAE+D,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAACN;QAClB;MACF,OAAO,IAAII,CAAC,CAACE,OAAO,CAACC,MAAK,KAAM,CAAC,EAAE;QACjC;QACAjE,UAAU,CAACI,KAAI,GAAI,KAAI;QACvBR,iBAAiB,CAACQ,KAAI,GAAIgD,WAAW,CAACU,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEF,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;QAChEnE,eAAe,CAACO,KAAI,GAAIwD,SAAS,CAACE,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEF,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;MAC9D;IACF;;IAEA;IACA,MAAME,eAAc,GAAKJ,CAAC,IAAK;MAC7BA,CAAC,CAACC,cAAc,CAAC;MAEjB,IAAID,CAAC,CAACE,OAAO,CAACC,MAAK,KAAM,KAAKjE,UAAU,CAACI,KAAK,EAAE;QAC9C;QACA,MAAM+D,MAAK,GAAIL,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAACR,OAAM,GAAIvD,YAAY,CAACG,KAAK,CAACN,CAAA;QACzD,MAAMsE,MAAK,GAAIN,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAACN,OAAM,GAAIzD,YAAY,CAACG,KAAK,CAACL,CAAA;QAEzDP,UAAU,CAACY,KAAI,IAAK+D,MAAK;QACzB1E,UAAU,CAACW,KAAI,IAAKgE,MAAK;QAEzBnE,YAAY,CAACG,KAAI,GAAI;UACnBN,CAAC,EAAEgE,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAACR,OAAO;UACvBzD,CAAC,EAAE+D,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAACN;QAClB;MACF,OAAO,IAAII,CAAC,CAACE,OAAO,CAACC,MAAK,KAAM,CAAC,EAAE;QACjC;QACA,MAAMI,QAAO,GAAIjB,WAAW,CAACU,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEF,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;QACvD,MAAMM,MAAK,GAAIV,SAAS,CAACE,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEF,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC;QAEnD,IAAIpE,iBAAiB,CAACQ,KAAI,GAAI,CAAC,EAAE;UAC/B,MAAMmE,WAAU,GAAIF,QAAO,GAAIzE,iBAAiB,CAACQ,KAAI;UACrD,MAAMoE,QAAO,GAAIzB,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzD,KAAK,CAACa,KAAI,GAAImE,WAAW,CAAC;;UAErE;UACA,MAAME,IAAG,GAAIpF,YAAY,CAACe,KAAK,CAACsE,qBAAqB,CAAC;UACtD,MAAMC,OAAM,GAAIL,MAAM,CAACxE,CAAA,GAAI2E,IAAI,CAACG,IAAG;UACnC,MAAMC,OAAM,GAAIP,MAAM,CAACvE,CAAA,GAAI0E,IAAI,CAACK,GAAE;UAElC,MAAMC,SAAQ,GAAIP,QAAO,GAAIjF,KAAK,CAACa,KAAI;UACvCZ,UAAU,CAACY,KAAI,IAAK,CAACuE,OAAM,GAAIF,IAAI,CAACpC,KAAI,GAAI,CAAC,IAAI0C,SAAQ,GAAIxF,KAAK,CAACa,KAAI;UACvEX,UAAU,CAACW,KAAI,IAAK,CAACyE,OAAM,GAAIJ,IAAI,CAACrC,MAAK,GAAI,CAAC,IAAI2C,SAAQ,GAAIxF,KAAK,CAACa,KAAI;UAExEb,KAAK,CAACa,KAAI,GAAIoE,QAAO;QACvB;QAEA5E,iBAAiB,CAACQ,KAAI,GAAIiE,QAAO;QACjCxE,eAAe,CAACO,KAAI,GAAIkE,MAAK;MAC/B;IACF;;IAEA;IACA,MAAMU,cAAa,GAAKlB,CAAC,IAAK;MAC5BA,CAAC,CAACC,cAAc,CAAC;MACjB/D,UAAU,CAACI,KAAI,GAAI,KAAI;MACvBR,iBAAiB,CAACQ,KAAI,GAAI;IAC5B;;IAEA;IACA,MAAM6E,WAAU,GAAKnB,CAAC,IAAK;MACzBA,CAAC,CAACC,cAAc,CAAC;MAEjB,MAAMU,IAAG,GAAIpF,YAAY,CAACe,KAAK,CAACsE,qBAAqB,CAAC;MACtD,MAAMC,OAAM,GAAIb,CAAC,CAACN,OAAM,GAAIiB,IAAI,CAACG,IAAG;MACpC,MAAMC,OAAM,GAAIf,CAAC,CAACJ,OAAM,GAAIe,IAAI,CAACK,GAAE;MAEnC,MAAMP,WAAU,GAAIT,CAAC,CAACM,MAAK,GAAI,IAAI,GAAE,GAAI,GAAE;MAC3C,MAAMI,QAAO,GAAIzB,IAAI,CAACG,GAAG,CAAC,GAAG,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzD,KAAK,CAACa,KAAI,GAAImE,WAAW,CAAC;MAErE,MAAMQ,SAAQ,GAAIP,QAAO,GAAIjF,KAAK,CAACa,KAAI;MACvCZ,UAAU,CAACY,KAAI,IAAK,CAACuE,OAAM,GAAIF,IAAI,CAACpC,KAAI,GAAI,CAAC,IAAI0C,SAAQ,GAAIxF,KAAK,CAACa,KAAI;MACvEX,UAAU,CAACW,KAAI,IAAK,CAACyE,OAAM,GAAIJ,IAAI,CAACrC,MAAK,GAAI,CAAC,IAAI2C,SAAQ,GAAIxF,KAAK,CAACa,KAAI;MAExEb,KAAK,CAACa,KAAI,GAAIoE,QAAO;IACvB;IAEA,OAAO;MACLzF,MAAM;MACN2D,WAAW;MACXvD,KAAK;MACLD,MAAM;MACNE,OAAO;MACPC,YAAY;MACZC,SAAS;MACTC,KAAK;MACLW,WAAW;MACX4C,MAAM;MACNG,OAAO;MACPE,SAAS;MACTU,gBAAgB;MAChBK,eAAe;MACfc,cAAc;MACdC;IACF;EACF;AACF", "ignoreList": []}]}