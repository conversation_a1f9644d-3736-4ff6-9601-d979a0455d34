<template>
  <div class="MajorProjectsColumnList">
    <van-overlay :show="loading" z-index="2000">
      <div style="display: flex; justify-content: center; align-items: center; height: 100vh;">
        <van-loading type="spinner" size="32px" color="#1989fa">加载中...</van-loading>
      </div>
    </van-overlay>
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="顶格推进重大项目" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <div v-for="(col, colIdx) in filteredColumnList" :key="colIdx" class="column-section">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">{{ col.label }}</span>
        </div>
        <div class="header_right" v-if="col.data.length > 3" @click="openMore(col.id)">
          <span class="header_right_more">更多</span>
          <img class="header_right_arrow" src="../../../assets/img/icon_word_right_arrow.png" alt="">
        </div>
      </div>
      <ul class="vue_newslist_box">
        <template v-if="col.data.length === 0">
          <div class="empty-tip">暂无数据</div>
        </template>
        <template v-else>
          <div v-for="item in col.data.slice(0, 3)" :key="item.id" class="van-hairline--bottom ">
            <van-cell clickable @click="openDetails(item)">
              <div class="major_project_item">
                <div class="major_project_left">
                  <div class="major_project_title">{{ item.title }}</div>
                  <div class="major_project_date">{{ dayjs(item.publishDate).format('YYYY-MM-DD') }}</div>
                </div>
                <img class="major_project_img" v-if="item.imageListVo[0]" :src="item.imageListVo[0].filePath" alt="" />
              </div>
            </van-cell>
          </div>
        </template>
      </ul>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, computed } from 'vue'
import { NavBar, Sticky, Tag, Loading, Overlay } from 'vant'
export default {
  name: 'MajorProjectsColumnList',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [Tag.name]: Tag,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      loading: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: [],
      columnListData: [
        { id: '1', label: '市政协领导孟庆斌顶格推进重大项目', data: [] },
        { id: '2', label: '市政协领导陈大维顶格推进重大项目', data: [] },
        { id: '3', label: '市政协领导姜巧珍顶格推进重大项目', data: [] },
        { id: '4', label: '市政协领导李苏满顶格推进重大项目', data: [] },
        { id: '5', label: '市政协领导张元升顶格推进重大项目', data: [] },
        { id: '6', label: '市政协领导薄涛顶格推进重大项目', data: [] },
        { id: '7', label: '市政协领导崔作顶格推进重大项目', data: [] }
      ]
    })
    // 添加计算属性来过滤有数据的栏目
    const filteredColumnList = computed(() => {
      return data.columnListData.filter(col => col.data.length > 0)
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    onMounted(() => {
      noticeList()
    })
    // 列表请求
    const noticeList = async () => {
      data.loading = true
      const res = await $api.ImportantWork.list({
        pageNo: 1,
        pageSize: 66,
        columnId: route.query.columnId
      })
      var { data: list } = res
      data.columnListData.forEach(item => { item.data = [] })
      list.forEach(item => {
        const col = data.columnListData.find(col => col.id === item.backupTwo)
        if (col) {
          col.data.push(item)
        }
      })
      data.loading = false
    }
    const openDetails = (row) => {
      router.push({ name: 'MajorProjectsDetails', query: { id: row.id } })
    }
    const onClickLeft = () => {
      history.back()
    }
    const openMore = (_id) => {
      router.push({ name: 'MajorProjectsList', query: { columnId: route.query.columnId, secondaryColumn: _id } })
    }
    return { ...toRefs(data), dayjs, openDetails, $general, onClickLeft, openMore, filteredColumnList }
  }
}
</script>
<style lang="less">
.MajorProjectsColumnList {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 15px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 18px;
        color: #222222;
        margin-left: 6px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_more {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_arrow {
        width: 23px;
        height: 23px;
      }
    }
  }

  .empty-tip {
    text-align: center;
    color: #999;
    padding: 32px 0;
    font-size: 15px;
  }

  .major_project_item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .major_project_left {
      flex: 1;
      margin-right: 12px;

      .major_project_title {
        font-family: Source Han Serif SC, Source Han Serif SC;
        // font-weight: bold;
        font-size: 18px;
        color: #333333;
        margin-bottom: 19px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .major_project_date {
        font-size: 14px;
        color: #999;
      }
    }

    .major_project_img {
      width: 121px;
      height: 103px;
      border-radius: 10px;
      object-fit: cover;
    }
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
