{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue?vue&type=style&index=0&id=8678c0f6&lang=less", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue", "mtime": 1756714397355}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudmFuLW5hdi1iYXIgew0KICBwYWRkaW5nLXRvcDogMzVweDsNCiAgYmFja2dyb3VuZDogcmdiKDIsIDExMywgMjI3KTsNCg0KICAudmFuLWljb24gew0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQoNCiAgLnZhbi1uYXYtYmFyX190aXRsZSB7DQogICAgZm9udC1zaXplOiAxN3B4Ow0KICAgIGNvbG9yOiAjZmZmOw0KICAgIGZvbnQtZmFtaWx5OiBzaW1wbGlmaWVkOw0KICB9DQp9DQoNCi5wZGZGaWxlUHJldmlldyB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMHZoOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBiYWNrZ3JvdW5kOiAjMDAwOw0KfQ0K"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\pdfFilePreview\\pdfFilePreview.vue"], "names": [], "mappings": ";AAmSA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/pdfFilePreview/pdfFilePreview.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"pdfFilePreview\">\r\n    <van-sticky>\r\n      <van-nav-bar v-if=\"hasApi\" :title=\"title\" left-text=\"\" left-arrow @click-left=\"onClickLeft\" />\r\n    </van-sticky>\r\n    <template v-if=\"pdfUrl\">\r\n      <!-- PDF 容器，支持手势缩放 -->\r\n      <div ref=\"pdfContainer\" class=\"pdf-container\" @touchstart=\"handleTouchStart\" @touchmove=\"handleTouchMove\"\r\n        @touchend=\"handleTouchEnd\" @wheel=\"handleWheel\">\r\n        <canvas ref=\"pdfCanvas\" class=\"pdf-canvas\" :style=\"canvasStyle\"></canvas>\r\n\r\n        <!-- 加载状态 -->\r\n        <div v-if=\"loading\" class=\"loading-overlay\">\r\n          <van-loading color=\"#1989fa\" />\r\n          <div>PDF加载中...</div>\r\n        </div>\r\n\r\n        <!-- 控制按钮 -->\r\n        <div class=\"pdf-controls\">\r\n          <van-button size=\"small\" @click=\"zoomOut\">-</van-button>\r\n          <span class=\"zoom-text\">{{ Math.round(scale * 100) }}%</span>\r\n          <van-button size=\"small\" @click=\"zoomIn\">+</van-button>\r\n          <van-button size=\"small\" @click=\"resetZoom\">重置</van-button>\r\n        </div>\r\n      </div>\r\n    </template>\r\n    <template v-else>\r\n      <div style=\"color:#fff;text-align:center;padding-top:40vh;\">未获取到PDF地址</div>\r\n    </template>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { computed, ref, onMounted, nextTick } from 'vue'\r\nimport { NavBar, Sticky, Button, Loading } from 'vant'\r\n\r\nexport default {\r\n  name: 'pdfFilePreview',\r\n  components: {\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Button.name]: Button,\r\n    [Loading.name]: Loading\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const pdfUrl = computed(() => route.query.url)\r\n    const hasApi = ref(true)\r\n    const title = ref(route.query.title)\r\n    const loading = ref(false)\r\n\r\n    // PDF 相关状态\r\n    const pdfContainer = ref(null)\r\n    const pdfCanvas = ref(null)\r\n    const scale = ref(1)\r\n    const translateX = ref(0)\r\n    const translateY = ref(0)\r\n    const pdfDoc = ref(null)\r\n    const currentPage = ref(1)\r\n\r\n    // 触摸相关状态\r\n    const lastTouchDistance = ref(0)\r\n    const lastTouchCenter = ref({ x: 0, y: 0 })\r\n    const isDragging = ref(false)\r\n    const lastTouchPos = ref({ x: 0, y: 0 })\r\n\r\n    // 计算样式\r\n    const canvasStyle = computed(() => ({\r\n      transform: `translate(${translateX.value}px, ${translateY.value}px) scale(${scale.value})`,\r\n      transformOrigin: 'center center',\r\n      transition: isDragging.value ? 'none' : 'transform 0.3s ease'\r\n    }))\r\n\r\n    if (title.value) {\r\n      document.title = title.value\r\n    }\r\n\r\n    // 加载 PDF.js\r\n    const loadPDFJS = async () => {\r\n      if (window.pdfjsLib) return\r\n\r\n      // 动态加载 PDF.js\r\n      const script = document.createElement('script')\r\n      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'\r\n      document.head.appendChild(script)\r\n\r\n      return new Promise((resolve) => {\r\n        script.onload = () => {\r\n          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'\r\n          resolve()\r\n        }\r\n      })\r\n    }\r\n\r\n    // 渲染 PDF\r\n    const renderPDF = async () => {\r\n      if (!pdfUrl.value || !pdfCanvas.value) return\r\n\r\n      loading.value = true\r\n\r\n      try {\r\n        await loadPDFJS()\r\n\r\n        const loadingTask = window.pdfjsLib.getDocument(pdfUrl.value)\r\n        pdfDoc.value = await loadingTask.promise\r\n\r\n        await renderPage(currentPage.value)\r\n      } catch (error) {\r\n        console.error('PDF 加载失败:', error)\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n\r\n    // 渲染页面\r\n    const renderPage = async (pageNum) => {\r\n      if (!pdfDoc.value || !pdfCanvas.value) return\r\n\r\n      const page = await pdfDoc.value.getPage(pageNum)\r\n      const canvas = pdfCanvas.value\r\n      const context = canvas.getContext('2d')\r\n\r\n      const viewport = page.getViewport({ scale: 1.5 })\r\n      canvas.height = viewport.height\r\n      canvas.width = viewport.width\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: viewport\r\n      }\r\n\r\n      await page.render(renderContext).promise\r\n    }\r\n\r\n    onMounted(async () => {\r\n      if (typeof (api) === 'undefined') {\r\n        hasApi.value = false\r\n      } else {\r\n        hasApi.value = true\r\n      }\r\n\r\n      await nextTick()\r\n      if (pdfUrl.value) {\r\n        await renderPDF()\r\n      }\r\n    })\r\n\r\n    const onClickLeft = () => {\r\n      if (typeof (api) === 'undefined') return history.back()\r\n      // eslint-disable-next-line no-undef\r\n      api.closeWin()\r\n    }\r\n\r\n    // 缩放控制\r\n    const zoomIn = () => {\r\n      scale.value = Math.min(scale.value * 1.2, 3)\r\n    }\r\n\r\n    const zoomOut = () => {\r\n      scale.value = Math.max(scale.value / 1.2, 0.5)\r\n    }\r\n\r\n    const resetZoom = () => {\r\n      scale.value = 1\r\n      translateX.value = 0\r\n      translateY.value = 0\r\n    }\r\n\r\n    // 获取两点间距离\r\n    const getDistance = (touch1, touch2) => {\r\n      const dx = touch1.clientX - touch2.clientX\r\n      const dy = touch1.clientY - touch2.clientY\r\n      return Math.sqrt(dx * dx + dy * dy)\r\n    }\r\n\r\n    // 获取两点中心\r\n    const getCenter = (touch1, touch2) => {\r\n      return {\r\n        x: (touch1.clientX + touch2.clientX) / 2,\r\n        y: (touch1.clientY + touch2.clientY) / 2\r\n      }\r\n    }\r\n\r\n    // 触摸开始\r\n    const handleTouchStart = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1) {\r\n        // 单指拖拽\r\n        isDragging.value = true\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        isDragging.value = false\r\n        lastTouchDistance.value = getDistance(e.touches[0], e.touches[1])\r\n        lastTouchCenter.value = getCenter(e.touches[0], e.touches[1])\r\n      }\r\n    }\r\n\r\n    // 触摸移动\r\n    const handleTouchMove = (e) => {\r\n      e.preventDefault()\r\n\r\n      if (e.touches.length === 1 && isDragging.value) {\r\n        // 单指拖拽\r\n        const deltaX = e.touches[0].clientX - lastTouchPos.value.x\r\n        const deltaY = e.touches[0].clientY - lastTouchPos.value.y\r\n\r\n        translateX.value += deltaX\r\n        translateY.value += deltaY\r\n\r\n        lastTouchPos.value = {\r\n          x: e.touches[0].clientX,\r\n          y: e.touches[0].clientY\r\n        }\r\n      } else if (e.touches.length === 2) {\r\n        // 双指缩放\r\n        const distance = getDistance(e.touches[0], e.touches[1])\r\n        const center = getCenter(e.touches[0], e.touches[1])\r\n\r\n        if (lastTouchDistance.value > 0) {\r\n          const scaleChange = distance / lastTouchDistance.value\r\n          const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n          // 以触摸中心为缩放中心\r\n          const rect = pdfContainer.value.getBoundingClientRect()\r\n          const centerX = center.x - rect.left\r\n          const centerY = center.y - rect.top\r\n\r\n          const scaleDiff = newScale - scale.value\r\n          translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n          translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n          scale.value = newScale\r\n        }\r\n\r\n        lastTouchDistance.value = distance\r\n        lastTouchCenter.value = center\r\n      }\r\n    }\r\n\r\n    // 触摸结束\r\n    const handleTouchEnd = (e) => {\r\n      e.preventDefault()\r\n      isDragging.value = false\r\n      lastTouchDistance.value = 0\r\n    }\r\n\r\n    // 鼠标滚轮缩放\r\n    const handleWheel = (e) => {\r\n      e.preventDefault()\r\n\r\n      const rect = pdfContainer.value.getBoundingClientRect()\r\n      const centerX = e.clientX - rect.left\r\n      const centerY = e.clientY - rect.top\r\n\r\n      const scaleChange = e.deltaY > 0 ? 0.9 : 1.1\r\n      const newScale = Math.max(0.5, Math.min(3, scale.value * scaleChange))\r\n\r\n      const scaleDiff = newScale - scale.value\r\n      translateX.value -= (centerX - rect.width / 2) * scaleDiff / scale.value\r\n      translateY.value -= (centerY - rect.height / 2) * scaleDiff / scale.value\r\n\r\n      scale.value = newScale\r\n    }\r\n\r\n    return {\r\n      pdfUrl,\r\n      onClickLeft,\r\n      title,\r\n      hasApi,\r\n      loading,\r\n      pdfContainer,\r\n      pdfCanvas,\r\n      scale,\r\n      canvasStyle,\r\n      zoomIn,\r\n      zoomOut,\r\n      resetZoom,\r\n      handleTouchStart,\r\n      handleTouchMove,\r\n      handleTouchEnd,\r\n      handleWheel\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\">\r\n.van-nav-bar {\r\n  padding-top: 35px;\r\n  background: rgb(2, 113, 227);\r\n\r\n  .van-icon {\r\n    color: #fff;\r\n  }\r\n\r\n  .van-nav-bar__title {\r\n    font-size: 17px;\r\n    color: #fff;\r\n    font-family: simplified;\r\n  }\r\n}\r\n\r\n.pdfFilePreview {\r\n  width: 100%;\r\n  height: 100vh;\r\n  overflow: hidden;\r\n  background: #000;\r\n}\r\n</style>\r\n"]}]}